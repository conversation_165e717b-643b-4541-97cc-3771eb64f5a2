#!/bin/bash

# =============================================================================
# Meta Agent Environment Setup Script
# =============================================================================
# This script sets up a Python virtual environment with all required packages
# =============================================================================

set -e  # Exit immediately if a command exits with a non-zero status

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Logging function
log_step() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')] STEP: $1${NC}"
}

log_info() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"
}

log_warn() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARN: $1${NC}"
}

log_error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

log_success() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] SUCCESS: $1${NC}"
}

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check Python version
check_python_version() {
    log_step "Checking Python version..."
    
    if command_exists python3; then
        PYTHON_CMD="python3"
    elif command_exists python; then
        PYTHON_CMD="python"
    else
        log_error "Python is not installed or not in PATH"
        exit 1
    fi
    
    PYTHON_VERSION=$($PYTHON_CMD --version 2>&1 | cut -d' ' -f2)
    PYTHON_MAJOR=$(echo $PYTHON_VERSION | cut -d'.' -f1)
    PYTHON_MINOR=$(echo $PYTHON_VERSION | cut -d'.' -f2)
    
    log_info "Found Python version: $PYTHON_VERSION"
    
    if [ "$PYTHON_MAJOR" -lt 3 ] || ([ "$PYTHON_MAJOR" -eq 3 ] && [ "$PYTHON_MINOR" -lt 12 ]); then
        log_error "Python 3.12 or higher is required. Found: $PYTHON_VERSION"
        exit 1
    fi
    
    log_success "Python version requirement satisfied"
}

# Function to handle existing virtual environment
handle_existing_venv() {
    if [ -d ".venv" ]; then
        log_warn "Existing .venv directory found!"
        echo -e "${YELLOW}What would you like to do?${NC}"
        echo "1) Use existing .venv (faster, but may have outdated packages)"
        echo "2) Remove and create new .venv (recommended for fresh setup)"
        echo "3) Exit setup"
        echo ""
        read -p "Enter your choice (1, 2, or 3): " choice
        
        case $choice in
            1)
                log_info "Using existing virtual environment..."
                return 0
                ;;
            2)
                log_info "Removing existing .venv and creating new one..."
                rm -rf .venv
                return 1
                ;;
            3)
                log_info "Setup cancelled by user"
                exit 0
                ;;
            *)
                log_error "Invalid choice. Please run the script again."
                exit 1
                ;;
        esac
    fi
    return 1  # No existing venv found, need to create new one
}

# Function to create virtual environment
create_venv() {
    log_step "Checking for existing virtual environment..."
    
    local use_existing=false
    if handle_existing_venv; then
        use_existing=true
        log_success "Using existing virtual environment"
    else
        log_step "Creating virtual environment..."
        log_info "Creating new virtual environment..."
        $PYTHON_CMD -m venv .venv
        
        if [ ! -d ".venv" ]; then
            log_error "Failed to create virtual environment"
            exit 1
        fi
        
        log_success "Virtual environment created successfully"
    fi
}

# Function to activate virtual environment
activate_venv() {
    log_step "Activating virtual environment..."
    
    if [ -f ".venv/bin/activate" ]; then
        source .venv/bin/activate
        log_success "Virtual environment activated"
    else
        log_error "Virtual environment activation script not found"
        exit 1
    fi
    
    # Verify activation
    if [ "$VIRTUAL_ENV" != "" ]; then
        log_info "Virtual environment path: $VIRTUAL_ENV"
    else
        log_error "Virtual environment activation failed"
        exit 1
    fi
}

# Function to upgrade pip and install uv
install_uv() {
    log_step "Installing uv package manager..."
    
    log_info "Upgrading pip..."
    python -m pip install --upgrade pip
    
    log_info "Installing uv..."
    python -m pip install uv
    
    # Verify uv installation
    if command_exists uv; then
        UV_VERSION=$(uv --version)
        log_success "uv installed successfully: $UV_VERSION"
    else
        log_error "uv installation failed"
        exit 1
    fi
}

# Function to check which packages are missing
check_missing_packages() {
    log_step "Checking which packages need to be installed..."
    
    if [ ! -f "requirements.txt" ]; then
        log_error "requirements.txt file not found"
        return 1
    fi
    
    local missing_packages=()
    local already_installed=()
    
    # Read requirements.txt and check each package
    while IFS= read -r line; do
        # Skip empty lines and comments
        if [[ -z "$line" || "$line" =~ ^[[:space:]]*# ]]; then
            continue
        fi
        
        # Extract package name (handle version specifiers)
        package_name=$(echo "$line" | sed 's/[><=!].*//' | sed 's/\[.*\]//')
        
        # Skip if package name is empty
        if [[ -z "$package_name" ]]; then
            continue
        fi
        
        # Check if package is installed
        if python -c "import pkg_resources; pkg_resources.get_distribution('$package_name')" 2>/dev/null; then
            already_installed+=("$package_name")
        else
            missing_packages+=("$line")  # Keep full line with version spec
        fi
    done < requirements.txt
    
    # Report results
    if [ ${#already_installed[@]} -gt 0 ]; then
        log_success "Already installed packages (${#already_installed[@]}): ${already_installed[*]:0:10}${#already_installed[@] -gt 10 && echo '...'}"
    fi
    
    if [ ${#missing_packages[@]} -gt 0 ]; then
        log_warn "Missing packages (${#missing_packages[@]}): $(echo "${missing_packages[@]}" | tr '\n' ' ' | head -c 100)${#missing_packages[@] -gt 5 && echo '...'}"
        
        # Create temporary requirements file with only missing packages
        printf "%s\n" "${missing_packages[@]}" > .requirements_missing.txt
        return 1  # Indicates packages need to be installed
    else
        log_success "All packages are already installed"
        return 0  # All packages already installed
    fi
}

# Function to install only missing packages
install_packages() {
    local attempt=$1
    log_step "Installing missing packages (Attempt $attempt/3)..."
    
    # First check what's missing
    if check_missing_packages; then
        log_success "No packages need to be installed"
        return 0
    fi
    
    # Install only missing packages
    if [ -f ".requirements_missing.txt" ]; then
        log_info "Installing $(wc -l < .requirements_missing.txt) missing packages using uv..."
        if uv pip install -r .requirements_missing.txt; then
            log_success "Missing packages installed successfully"
            # Clean up temporary file
            rm -f .requirements_missing.txt
            return 0
        else
            log_error "Package installation failed on attempt $attempt"
            return 1
        fi
    else
        log_error "Missing packages list not found"
        return 1
    fi
}

# Function to validate installed packages
validate_packages() {
    log_step "Validating installed packages..."
    
    local failed_packages=()
    
    # Read requirements.txt and check each package
    while IFS= read -r line; do
        # Skip empty lines and comments
        if [[ -z "$line" || "$line" =~ ^[[:space:]]*# ]]; then
            continue
        fi
        
        # Extract package name (handle version specifiers)
        package_name=$(echo "$line" | sed 's/[><=!].*//' | sed 's/\[.*\]//')
        
        # Skip if package name is empty
        if [[ -z "$package_name" ]]; then
            continue
        fi
        
        log_info "Checking package: $package_name"
        
        if python -c "import pkg_resources; pkg_resources.get_distribution('$package_name')" 2>/dev/null; then
            log_info "✓ $package_name is installed"
        else
            log_warn "✗ $package_name is NOT installed"
            failed_packages+=("$package_name")
        fi
    done < requirements.txt
    
    if [ ${#failed_packages[@]} -eq 0 ]; then
        log_success "All packages are installed correctly"
        return 0
    else
        log_error "Missing packages: ${failed_packages[*]}"
        return 1
    fi
}



# Function to create activation script
create_activation_script() {
    log_step "Creating environment activation script..."
    
    cat > setup/activate_env.sh << 'EOF'
#!/bin/bash
# Environment Activation Script for Meta Agent
echo "Activating Meta Agent environment..."

# Change to project root directory
cd "$(dirname "$0")/.."

# Activate virtual environment
source .venv/bin/activate

echo "Environment activated! You can now run the application."
echo "Python version: $(python --version 2>/dev/null || python3 --version)"
echo "Virtual environment: $VIRTUAL_ENV"
echo "Current directory: $(pwd)"

# Create python symlink if needed
if ! command -v python >/dev/null 2>&1 && command -v python3 >/dev/null 2>&1; then
    echo "Creating python -> python3 symlink in virtual environment..."
    ln -sf "$(which python3)" .venv/bin/python
fi
EOF
    
    chmod +x setup/activate_env.sh
    log_success "Activation script created at setup/activate_env.sh"
}

# Function to create environment info script
create_env_info_script() {
    log_step "Creating environment information script..."
    
    cat > setup/env_info.sh << 'EOF'
#!/bin/bash
# Environment Information Script
echo "=== Meta Agent Environment Information ==="
echo "Python version: $(python --version)"
echo "Virtual environment: $VIRTUAL_ENV"
echo "Pip version: $(pip --version)"
echo "UV version: $(uv --version)"
echo ""
echo "=== Installed Packages ==="
pip list
EOF
    
    chmod +x setup/env_info.sh
    log_success "Environment info script created at setup/env_info.sh"
}

# Main setup function
main() {
    echo -e "${PURPLE}"
    echo "============================================================="
    echo "         Meta Agent Environment Setup Script"
    echo "============================================================="
    echo -e "${NC}"
    
    # Change to project root directory
    cd "$(dirname "$0")/.."
    
    log_info "Starting environment setup in: $(pwd)"
    
    # Step 1: Check Python version
    check_python_version
    
    # Step 2: Create virtual environment
    create_venv
    
    # Step 3: Activate virtual environment
    activate_venv
    
    # Step 4: Install uv
    install_uv
    
    # Step 5: Install packages with retry logic
    local max_attempts=3
    local attempt=1
    local install_success=false
    
    while [ $attempt -le $max_attempts ]; do
        if install_packages $attempt; then
            install_success=true
            break
        else
            if [ $attempt -lt $max_attempts ]; then
                log_warn "Retrying in 5 seconds..."
                sleep 5
            fi
            ((attempt++))
        fi
    done
    
    if [ "$install_success" = false ]; then
        log_error "Package installation failed after $max_attempts attempts"
        exit 1
    fi
    
    # Step 6: Final validation with retry logic
    log_step "Final package validation..."
    local validation_attempts=3
    local validation_attempt=1
    local validation_success=false

    while [ $validation_attempt -le $validation_attempts ]; do
        if validate_packages; then
            log_success "All packages validated successfully"
            validation_success=true
            break
        else
            if [ $validation_attempt -lt $validation_attempts ]; then
                log_warn "Validation failed. Retrying in 3 seconds... (Attempt $validation_attempt/$validation_attempts)"
                sleep 3
            fi
            ((validation_attempt++))
        fi
    done

    if [ "$validation_success" = false ]; then
        log_error "Package validation failed after $validation_attempts attempts"
        log_info "Some packages may still be missing. You may need to install them manually or re-run the setup"
    fi

    # Step 7: Create helper scripts
    create_activation_script
    create_env_info_script
    
    # Final summary
    echo -e "${GREEN}"
    echo "============================================================="
    echo "              SETUP COMPLETED SUCCESSFULLY!"
    echo "============================================================="
    echo -e "${NC}"
    
    log_success "Virtual environment created at: $(pwd)/.venv"
    log_success "All packages installed and validated"
    log_success "Helper scripts created in setup/ folder"
    
    # Log final environment state before final activation
    echo -e "${BLUE}=== Final Environment State ===${NC}"
    echo "Python executable: $(which python || which python3)"
    echo "Python version: $(python --version)"
    echo "Virtual environment: $VIRTUAL_ENV"
    echo "Total packages installed: $(pip list | wc -l)"
    
    # Create python symlink if needed for better compatibility
    if ! command -v python >/dev/null 2>&1 && command -v python3 >/dev/null 2>&1; then
        log_info "Creating python -> python3 symlink for better compatibility..."
        ln -sf "$(which python3)" .venv/bin/python
        log_success "Python symlink created"
    fi
    
    echo -e "${CYAN}"
    echo "🎉 Environment is ready and automatically activated!"
    echo ""
    echo "You can now:"
    echo "• Start your application directly"
    echo "• Run: python main.py (or your main application file)"
    echo "• Use: './setup/env_info.sh' to check environment info"
    echo "• Re-activate anytime with: 'source .venv/bin/activate'"
    echo -e "${NC}"
    
    echo -e "${PURPLE}Environment is active and ready to use! 🚀${NC}"
    
    # Return to user with activated environment
    # Don't exit the script, let the user continue in the activated shell
}

# Error handling
trap 'log_error "Setup script failed at line $LINENO. Exit code: $?"' ERR

# Run main function
main "$@" 